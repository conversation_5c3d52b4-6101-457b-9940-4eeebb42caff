import React, { useEffect, useState, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import '../styles/ParkDetailPage.css';
import '../styles/DetailPageSideBar.css';
import '../styles/TransportLayer.css';
import translations from '../utils/translations';
import { MapContainer, TileLayer, Marker, Popup, Polyline, Polygon, useMap, ScaleControl } from 'react-leaflet';
import { calculateDistance, formatDistance } from '../utils/mapUtils';
import 'leaflet/dist/leaflet.css';
import L from 'leaflet';
import MapStyleSwitcher from '../components/map/MapStyleSwitcher';
import '../styles/MapStyleSwitcher.css';
import '../styles/ScaleControl.css';
import '../styles/FacilitySearch.css';
import '../styles/WeatherTool.css'; // 添加天气工具样式
import '../styles/leaflet-popup-styles.css'; // 引入弹窗样式覆盖
import RulerTool from '../components/controls/RulerTool';
// 3D按钮已移除
import FacilitySearch from '../components/search/FacilitySearch';
import DetailMapZoomControl from '../components/detail-map/DetailMapZoomControl';
import WeatherModule from '../components/controls/WeatherModule';
// 导入新的环境监测图层组件
import EnvironmentMonitoringLayer from '../components/layers/EnvironmentMonitoringLayer';
import '../styles/EnvironmentMonitoringLayer.css'; // 导入环境监测图层样式

// 导入新的迁移类型图层组件
import MigrationTypeLayer from '../components/layers/MigrationTypeLayer';
import '../styles/MigrationTypeLayer.css'; // 导入迁移类型图层样式
import '../styles/MigrationControls.css'; // 导入迁移控制面板样式

// 原有组件
import ParkCharts from '../components/detail-map/ParkCharts';
import LaborDataCharts from '../components/analysis/labor/LaborDataCharts';
// 新增组件：劳动趋势图层与数据面板
import LaborTrendOverlay from '../components/layers/LaborTrendOverlay';
import LaborTrendDataPanel from '../components/analysis/labor/LaborTrendDataPanel';
// 新增劳动力经济分析组件
import LaborEconomicsAnalysis from '../components/analysis/labor/LaborEconomicsAnalysis';
// 导入新的 ParkOverview 组件
import ParkOverview from '../components/detail-map/ParkOverview';
import '../styles/LaborDataCharts.css';
import MapTimeSlider from '../components/controls/MapTimeSlider';
import TimeSeriesPanel from '../components/analysis/TimeSeriesPanel';
import '../styles/TimeSeriesPanel.css';
import PopulationMigrationAnalysis from '../components/analysis/migration/PopulationMigrationAnalysis';
// 导入新的就业迁移分析组件
import HouseholdRegistrationAnalysis from '../components/analysis/migration/HouseholdRegistrationAnalysis';
import EmploymentMigrationAnalysis from '../components/analysis/migration/EmploymentMigrationAnalysis';
import '../styles/EmploymentMigrationAnalysis.css';
import EducationLevelAnalysis from '../components/analysis/migration/EducationLevelAnalysis';
import '../styles/EducationLevelAnalysis.css';
import MaritalStatusAnalysis from '../components/analysis/migration/MaritalStatusAnalysis';
import '../styles/MaritalStatusAnalysis.css';
import MigrationTypeAnalysis from '../components/analysis/migration/MigrationTypeAnalysis';
import '../styles/MigrationTypeAnalysis.css';
import MigrationFrequencyAnalysis from '../components/analysis/migration/MigrationFrequencyAnalysis';
import '../styles/MigrationFrequencyAnalysis.css';
import ExpectedStayAnalysis from '../components/analysis/migration/ExpectedStayAnalysis';
import '../styles/ExpectedStayAnalysis.css';
import FutureMigrationReasonAnalysis from '../components/analysis/migration/FutureMigrationReasonAnalysis';
import '../styles/FutureMigrationReasonAnalysis.css';
import MoneyGoodsReceiptAnalysis from '../components/analysis/migration/MoneyGoodsReceiptAnalysis';
import '../styles/MoneyGoodsReceiptAnalysis.css';
import MoneyGoodsSenderAnalysis from '../components/analysis/migration/MoneyGoodsSenderAnalysis';
import MoneyGoodsSendingAnalysis from '../components/analysis/migration/MoneyGoodsSendingAnalysis';
import '../styles/MoneyGoodsSendingAnalysis.css';
import '../styles/MoneyGoodsSenderAnalysis.css';
import MoneyPurposeAnalysis from '../components/analysis/migration/MoneyPurposeAnalysis';
import '../styles/MoneyPurposeAnalysis.css';
import MoneyGoodsRecipientAnalysis from '../components/analysis/migration/MoneyGoodsRecipientAnalysis';
import '../styles/MoneyGoodsRecipientAnalysis.css';
import MoneySendingPurposeAnalysis from '../components/analysis/migration/MoneySendingPurposeAnalysis';
import '../styles/MoneySendingPurposeAnalysis.css';
// 乌兹别克斯坦交通分析
import UzTransportAnalysis from '../components/analysis/transport/UzTransportAnalysis';

// 修复 Leaflet 默认图标问题
delete L.Icon.Default.prototype._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon-2x.png',
  iconUrl: 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon.png',
  shadowUrl: 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-shadow.png',
});

// 自定义图标
const customIcon = (color) =>
  L.divIcon({
    className: 'custom-div-icon',
    html: `<div style="background-color: ${color}; width: 12px; height: 12px; border-radius: 50%; border: 2px solid white;"></div>`,
    iconSize: [15, 15],
    iconAnchor: [7, 7],
  });

// 自定义SVG图标
const svgIcon = (iconUrl) => {
  return L.icon({
    iconUrl: iconUrl,
    iconSize: [32, 42],
    iconAnchor: [16, 42],
    popupAnchor: [0, -40],
  });
};

// 地图控制组件（用于飞行动画）
const MapController = ({ center, zoom }) => {
  const map = useMap();
  // 跟踪是否已经执行过飞行动画
  const hasFlownRef = useRef(false);

  useEffect(() => {
    // 只有当中心点和缩放级别有效，且还没有飞行过时才执行飞行动画
    if (center && zoom && !hasFlownRef.current) {
      // 使用更平滑的动画参数
      map.flyTo(center, zoom, {
        duration: 1.8,           // 增加持续时间
        easeLinearity: 0.3,      // 降低线性度
        noMoveStart: true        // 避免开始移动事件
      });
      // 标记为已执行过飞行动画
      hasFlownRef.current = true;
    } else if (center && zoom) {
      // 如果已经飞行过，直接设置位置，不使用动画
      map.setView(center, zoom, { animate: false });
    }
  }, [center, zoom, map]);

  return null;
};

function parseNumber(str) {
  if (!str) return 0;
  return parseInt(str.replace(/,/g, ''), 10) || 0;
}

function getFillColor(employed) {
  const minVal = 11000000;
  const maxVal = 14000000;
  const ratio = Math.max(0, Math.min(1, (employed - minVal) / (maxVal - minVal)));
  const g = 153 + Math.round(ratio * (214 - 153));
  return `rgba(255, ${g}, 153, 0.6)`;
}

  // 判断是否为乌兹别克斯坦·大顺集团园区（Nurafshon）的辅助函数
  const isUzDashunPark = (p) => {
    if (!p) return false;
    const name = (p.name || '').toLowerCase();
    const dev = (p.developer || '').toLowerCase();
    return (
      p.id === 'dashun-nurafshon' ||
      name.includes('dashun') ||
      dev.includes('dashun') ||
      ((p.iso2 || '').toUpperCase() === 'UZ' && ((p.city || '').toLowerCase() === 'nurafshon' || (p.admin_name || '').toLowerCase() === 'toshkent'))
    );
  };

  // 简单判断是否为泰国园区（当前仅支持泰国与乌兹别克斯坦两类数据）
  const isThaiPark = (p) => {
    if (!p) return false;
    const iso = (p.iso2 || '').toUpperCase();
    // 只要不是乌兹别克斯坦演示园区，就视为泰国园区
    return !isUzDashunPark(p) && iso !== 'UZ';
  };

// MapTimeSlider组件已移至独立文件

const ParkDetailPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [parkItem, setParkItem] = useState(null);
  // 从 localStorage 回退读取（从主界面点击进入时存储）
  useEffect(() => {
    if (!parkItem) {
      try {
        const saved = localStorage.getItem('selectedPark');
        if (saved) {
          const obj = JSON.parse(saved);
          setParkItem(obj);
        }
      } catch (e) {
        console.warn('读取 selectedPark 失败:', e);
      }
    }
  }, [parkItem]);

  const [loading, setLoading] = useState(true);
  const [parkData, setParkData] = useState(null);
  const [imagesLoaded, setImagesLoaded] = useState({ main: false, layout: false });
  const [showBoundary, setShowBoundary] = useState(true); // 修改为默认显示园区轮廓
  const [language, setLanguage] = useState(() => localStorage.getItem('preferredLanguage') || 'en');

  const [showLaborTrendLayer, setShowLaborTrendLayer] = useState(false); // 修改为默认不显示
  const [showTransportLayer, setShowTransportLayer] = useState(false); // 交通图层显示状态
  const [laborGeoData, setLaborGeoData] = useState(null);
  const [laborTrends, setLaborTrends] = useState([]);
  const [selectedTrendIndex, setSelectedTrendIndex] = useState(0);

  // 新增环境监测图层状态
  const [showEnvironmentLayer, setShowEnvironmentLayer] = useState(false);
  const [environmentData, setEnvironmentData] = useState(null);
  const [environmentType, setEnvironmentType] = useState('air'); // 'air', 'water', 'noise', 'wind'

  // 新增迁移类型图层状态
  const [showMigrationLayer, setShowMigrationLayer] = useState(false);
  const [selectedGender, setSelectedGender] = useState('Total'); // 'Total', 'Male', 'Female'
  const [selectedMigrationType, setSelectedMigrationType] = useState('All'); // 'All', 'Whole_Household_Migrated', 'Partial_Household_Migrated', 'Individual_Migrated'

  // 新增状态：地图中心点和缩放级别
  const [mapCenter, setMapCenter] = useState([13.7563, 100.5018]);
  const [mapZoom, setMapZoom] = useState(4);

  // 添加天气模块状态
  const [showWeatherModule, setShowWeatherModule] = useState(false);

  // activeSection: 'map', 'overview', 'laborTrends', 'laborEconomics', 'parkLayout', 'contactInfo', 'parkHighlights'
  const [activeSection, setActiveSection] = useState('map');
  const [isMapView, setIsMapView] = useState(true);
  const [mapStyle, setMapStyle] = useState('day'); // 添加地图样式状态：day, night, satellite
  const [showRulerTool, setShowRulerTool] = useState(false); // 控制尺子工具的显示与隐藏
  // 3D地图模式状态已移除
  const [showTimeSeriesPanel, setShowTimeSeriesPanel] = useState(false);

  const mapRef = useRef(null);
  const parkMarkerRef = useRef(null);

  // 安全地触发 Leaflet 尺寸重计算，避免右侧出现空白
  const invalidateMapSizeSafely = () => {
    try {
      if (mapRef.current && typeof mapRef.current.invalidateSize === 'function') {
        // 使用微小延时，等待容器布局稳定后再触发
        setTimeout(() => {
          try {
            mapRef.current.invalidateSize(false);
          } catch (e) {
            console.warn('invalidateSize 调用失败:', e);
          }
        }, 150);
      }
    } catch (error) {
      console.warn('触发地图尺寸更新时出错:', error);
    }
  };

  // 监听窗口和容器尺寸变化，避免地图因容器变化出现未铺满的空白
  useEffect(() => {
    const handleResize = () => invalidateMapSizeSafely();
    window.addEventListener('resize', handleResize);
    window.addEventListener('orientationchange', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('orientationchange', handleResize);
    };
  }, []);

  // 不同地图样式的URL和属性信息
  const mapTileUrls = {
    day: "https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",
    night: "https://tiles.stadiamaps.com/tiles/alidade_smooth_dark/{z}/{x}/{y}.png",
    satellite: "https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}"
  };

  const mapAttributions = {
    day: "© OpenStreetMap contributors",
    night: "© Stadia Maps, OpenMapTiles, OpenStreetMap contributors",
    satellite: "© Esri — Source: Esri, i-cubed, USDA, USGS, AEX, GeoEye, Getmapping, Aerogrid, IGN, IGP, UPR-EGP, and the GIS User Community"
  };

  // 预设园区边界坐标（Marker 点击时缩放）
  const pinkPolygonCoords = [
    [13.9339407409134, 101.5720641128539],
    [13.943860122456272, 101.58343280408333],
    [13.918893674766531, 101.5957201774323],
    [13.90529474287192, 101.59744270640644],
    [13.898049083956323, 101.61512733720777],
    [13.901950620309625, 101.57493499431779],
    [13.888127736609492, 101.56724103156655],
    [13.899944123867103, 101.54209210854388],
    [13.923240705418529, 101.54312562592987],
    [13.935166754763125, 101.55380530556964],
    [13.928033490830254, 101.56333663255995],
    [13.924578236928383, 101.5697674073968],
    [13.934498020628387, 101.56965257213186]
  ];

  // 使用useRef跟踪初始化状态，避免重复执行flyTo
  const initialFlyDoneRef = useRef(false);
  // 使用sessionStorage跟踪是否已经显示过飞入动画
  const hasShownInitialAnimation = useRef(sessionStorage.getItem('parkDetailInitialFlyDone') === 'true');
  // 检查是否是从其他视图切换回地图视图
  const isSwitchedToMap = useRef(sessionStorage.getItem('parkDetailSwitchedToMap') === 'true');
  // 跟踪是否已经打开过弹窗
  const hasOpenedPopupRef = useRef(false);

  useEffect(() => {
    try {
      const current = parkData || parkItem;
      // 只有当没有显示过飞入动画且不是从其他视图切换回地图视图时才执行飞入动画
      if (current && mapRef.current && !initialFlyDoneRef.current &&
          !hasShownInitialAnimation.current && !isSwitchedToMap.current) {
        // 等待地图完全加载
        setTimeout(() => {
          if (isUzDashunPark(current) && current.position) {
            mapRef.current.flyTo(current.position, 12, {
              duration: 1.5,
              easeLinearity: 0.5,
              noMoveStart: true
            });
          } else {
            // 默认：缩放到预设园区轮廓边界
            const bounds = L.polygon(pinkPolygonCoords).getBounds();
            mapRef.current.flyToBounds(bounds, {
              duration: 1.8,
              easeLinearity: 0.3,
              padding: [30, 30],
              noMoveStart: true
            });
          }

          // 标记为已完成初始飞行
          initialFlyDoneRef.current = true;
          sessionStorage.setItem('parkDetailInitialFlyDone', 'true');
          hasShownInitialAnimation.current = true;
        }, 800);

        // 动画开始后与结束前后，均尝试触发一次尺寸校正
        invalidateMapSizeSafely();
      } else if (current && mapRef.current && !initialFlyDoneRef.current) {
        if (isUzDashunPark(current) && current.position) {
          mapRef.current.setView(current.position, 12, { animate: false });
        } else {
          const bounds = L.polygon(pinkPolygonCoords).getBounds();
          mapRef.current.fitBounds(bounds, { padding: [30, 30] });
        }
        initialFlyDoneRef.current = true;
        invalidateMapSizeSafely();
      }
    } catch (error) {
      console.error("地图飞行初始化出错:", error);
    }

    // 清除切换标记，以便下次进入时重新评估
    return () => {
      sessionStorage.removeItem('parkDetailSwitchedToMap');
      // 不清除 originalMapState
    };
  }, [parkData, parkItem]);

  // 初始加载时：更新地图中心点和缩放级别
  useEffect(() => {
    if (parkData || parkItem) {
      const position = parkData?.position || parkItem?.position;
      if (position) {
        setMapCenter(position);
        setMapZoom(4);
      }
    }

    // 添加页面卸载事件监听器，确保在返回主页面时保留originalMapState
    const handleBeforeUnload = () => {
      // 不做任何操作，保留originalMapState
      console.log('Detail page is being unloaded, preserving originalMapState for restoration');
    };

    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
      invalidateMapSizeSafely();
    };
  }, [parkData, parkItem]);

  // 已移除自动打开标记弹窗的逻辑，因为详情页面不需要显示弹窗
  useEffect(() => {
    // 在详情页面中，我们不需要自动打开弹窗
    // 这里只是确保当数据加载时更新地图位置
    if ((parkData || parkItem) && mapRef.current) {
      console.log('数据加载完成，但不自动打开弹窗');
      // 如果弹窗已经打开，关闭它
      if (parkMarkerRef.current && parkMarkerRef.current.isPopupOpen()) {
        parkMarkerRef.current.closePopup();
        console.log('关闭了已打开的弹窗');
      }
      hasOpenedPopupRef.current = false; // 重置标记，确保弹窗不会被打开
    }
  }, [parkData, parkItem]);

  // 加载园区数据（支持泰国与乌兹别克斯坦演示）
  useEffect(() => {
    const loadThai = async () => {
      const res = await fetch('/data/thai_parks_data.json');
      const data = await res.json();
      const parkName = id;
      return data.find(p => p.name === parkName) || null;
    };
    const loadUz = async () => {
      try {
        const res = await fetch('/data/Uzbekistan_data/industrial_parks.json');
        const data = await res.json();
        const parkName = id;
        // id 优先匹配，其次按 name 匹配
        return (Array.isArray(data) ? data : []).find(p => p.id === parkName || p.name === parkName) || null;
      } catch (e) {
        console.error('加载乌兹别克斯坦园区演示数据失败:', e);
        return null;
      }
    };

    (async () => {
      try {
        let park = await loadThai();
        if (!park) {
          park = await loadUz();
        }
        if (park) setParkData(park);
        else console.error('未找到匹配的园区数据:', id);
      } catch (error) {
        console.error('处理园区数据时出错:', error);
      } finally {
        setLoading(false);
      }
    })();
  }, [id]);

  // 加载中央区域 GeoJSON + 劳动力数据
  // 使用useRef跟踪GeoJSON加载状态，避免重复执行fitBounds
  const geoJsonLoadedRef = useRef(false);

  useEffect(() => {
    fetch('/data/thailand-provinces.geojson')
      .then(res => {
        if (!res.ok) throw new Error(`加载 thailand-provinces.geojson 失败: ${res.status}`);
        return res.json();
      })
      .then(data => {
        // 过滤出中央区域的省份
        // 泰国中央区域包括：Bangkok, Samut Prakan, Nonthaburi, Pathum Thani,
        // Samut Sakhon, Nakhon Pathom, Samut Songkhram, Ayutthaya, Ang Thong,
        // Lopburi, Sing Buri, Chai Nat, Saraburi, Nakhon Nayok, Suphan Buri, Kanchanaburi
        const centralProvinces = [
          'Bangkok', 'Samut Prakan', 'Nonthaburi', 'Pathum Thani',
          'Samut Sakhon', 'Nakhon Pathom', 'Samut Songkhram', 'Phra Nakhon Si Ayutthaya',
          'Ang Thong', 'Lopburi', 'Sing Buri', 'Chai Nat', 'Saraburi',
          'Nakhon Nayok', 'Suphan Buri', 'Kanchanaburi', 'Prachinburi'
        ];

        // 过滤出中央区域的省份
        const centralRegionData = {
          type: "FeatureCollection",
          features: data.features.filter(feature =>
            centralProvinces.includes(feature.properties.NAME_1)
          )
        };

        setLaborGeoData(centralRegionData);

        // 只有在初始化flyTo完成后才执行fitBounds，避免冲突
        if (mapRef.current && centralRegionData && initialFlyDoneRef.current && !geoJsonLoadedRef.current) {
          setTimeout(() => {
            const layer = L.geoJSON(centralRegionData);
            const bounds = layer.getBounds();
            mapRef.current.fitBounds(bounds, { padding: [20, 20] });
            geoJsonLoadedRef.current = true;
          }, 1500);
        }
      })
      .catch(err => console.error('加载 GeoJSON 出错:', err));

    fetch('/data/Labor_trends_Gis_Rendering.json')
      .then(res => {
        if (!res.ok) throw new Error(`加载 Labor_trends_Gis_Rendering.json 失败: ${res.status}`);
        return res.json();
      })
      .then(data => {
        setLaborTrends(data);
        setSelectedTrendIndex(0);
      })
      .catch(err => console.error('加载劳动力数据出错:', err));
  }, []);

  // 加载环境监测数据
  useEffect(() => {
    fetch('/data/environment_monitoring.geojson')
      .then(res => {
        if (!res.ok) throw new Error(`加载环境监测数据失败: ${res.status}`);
        return res.json();
      })
      .then(data => {
        setEnvironmentData(data);
      })
      .catch(err => console.error('加载环境监测数据出错:', err));
  }, []);

  const handleImageLoad = (type) => {
    setImagesLoaded(prev => ({ ...prev, [type]: true }));
  };

  const handleImageError = (e, type) => {
    e.target.src = '/placeholder.png';
    setImagesLoaded(prev => ({ ...prev, [type]: true }));
  };

  const toggleLanguage = () => {
    const newLang = language === 'zh' ? 'en' : 'zh';
    setLanguage(newLang);
    localStorage.setItem('preferredLanguage', newLang);
  };

  const resetMap = () => {
    // 重置为园区轮廓视图，而不是默认位置
    if (mapRef.current) {
      const bounds = L.polygon(pinkPolygonCoords).getBounds();
      mapRef.current.flyToBounds(bounds, { duration: 1.5, easeLinearity: 0.5, padding: [20, 20] });
      setShowBoundary(true); // 确保显示园区轮廓
    }
  };


  // 根据不同园区渲染不同的自定义页头/版块
  const renderParkSpecificHeader = (p) => {
    if (isUzDashunPark(p)) {
      return (
        <div className="park-header-container uz-park">
          <div className="park-header-main">
            <div className="park-title-section">
              <h1 className="park-name-primary">Dashun Group Industrial Park</h1>
              <h2 className="park-name-secondary">大顺集团产业园（努拉夫尚）</h2>
            </div>
            <div className="park-location-section">
              <div className="location-item">
                <span className="location-icon">📍</span>
                <span className="location-text">Toshkent Viloyati, Nurafshon, Birlik MFY, Tashkent Road 78, 050020, Uzbekistan</span>
              </div>
            </div>
            <div className="park-meta-section">
              <div className="meta-row">
                <span className="meta-chip developer">
                  <span className="chip-icon">🏢</span>
                  <span className="chip-text">DASHUN GROUP</span>
                </span>
                <span className="meta-chip city">
                  <span className="chip-icon">🏙️</span>
                  <span className="chip-text">Nurafshon</span>
                </span>
                <span className="meta-chip region">
                  <span className="chip-icon">🗺️</span>
                  <span className="chip-text">Toshkent</span>
                </span>
              </div>
            </div>
            <div className="park-coordinates-section">
              <div className="coordinate-item">
                <span className="coord-label">Latitude</span>
                <span className="coord-value">41.0205</span>
              </div>
              <div className="coordinate-item">
                <span className="coord-label">Longitude</span>
                <span className="coord-value">69.3621</span>
              </div>
            </div>
          </div>
        </div>
      );
    }
    // 泰国园区头部
    return (
      <div className="park-header-container thai-park">
        <div className="park-header-main">
          <div className="park-title-section">
            <h1 className="park-name-primary">{p.name}</h1>
          </div>
          <div className="park-location-section">
            <div className="location-item">
              <span className="location-icon">📍</span>
              <span className="location-text">{p.province}</span>
            </div>
          </div>
          {p.position && (
            <div className="park-coordinates-section">
              <div className="coordinate-item">
                <span className="coord-label">Latitude</span>
                <span className="coord-value">{p.position[0].toFixed(4)}</span>
              </div>
              <div className="coordinate-item">
                <span className="coord-label">Longitude</span>
                <span className="coord-value">{p.position[1].toFixed(4)}</span>
              </div>
            </div>
          )}
        </div>
      </div>
    );
  };

  const t = translations[language];
  const park = parkData || parkItem;
  if (!park) {
    return (
      <div className="error-container">
        <h2>{t.notFound}</h2>
        <p>{t.cannotLoad} "{id}"</p>
        <button className="back-button" onClick={() => navigate('/main')}>
          {t.backToMap}
        </button>
      </div>
    );
  }
  const currentParkName = park.name || '未命名园区';
  const isThai = isThaiPark(park);

  // 根据劳动力数值计算对应颜色
  let fillColor = 'rgba(255,153,153,0.6)';
  if (laborTrends.length > 0) {
    const currentRecord = laborTrends[Math.min(selectedTrendIndex, laborTrends.length - 1)];
    const employed = parseNumber(currentRecord["Employed Persons "]);
    fillColor = getFillColor(employed);
  }

  // 侧边栏菜单（泰国园区显示完整分析，其他园区仅显示基础信息）
  const sidebarNavItems = isThai ? [
    { id: 'map', icon: '🗺️', label: t.map || '地图' },
    { id: 'overview', icon: '📋', label: t.overview || '概览' },
    { id: 'laborTrends', icon: '📈', label: t.laborTrends || '劳动力趋势' },
    { id: 'laborEconomics', icon: '📉', label: t.laborEconomics || '劳动力经济分析' },
    { id: 'laborMarket', icon: '📊', label: t.laborMarketAnalysis || 'Labor Market Analysis' },
    { id: 'populationMigration', icon: '👥', label: t.populationMigrationAnalysis || 'Population Migration Analysis' },
    { id: 'employmentMigration', icon: '👔', label: t.employmentMigrationAnalysis || 'Employment Migration Analysis' },
    { id: 'householdRegistration', icon: '🏠', label: t.householdRegistrationAnalysis || 'Household Registration Analysis' },
    { id: 'educationLevel', icon: '🎓', label: t.educationLevelAnalysis || 'Education Level Analysis' },
    { id: 'maritalStatus', icon: '💍', label: t.maritalStatusAnalysis || 'Marital Status Analysis' },
    { id: 'migrationType', icon: '🔄', label: t.migrationTypeAnalysis || 'Migration Type Analysis' },
    { id: 'migrationFrequency', icon: '🔢', label: t.migrationFrequencyAnalysis || 'Migration Frequency Analysis' },
    { id: 'expectedStay', icon: '⏱️', label: t.expectedStayAnalysis || 'Expected Stay Duration Analysis' },
    { id: 'futureMigrationReason', icon: '🔮', label: t.futureMigrationReasonAnalysis || 'Future Migration Reason Analysis' },
    { id: 'moneyGoodsReceipt', icon: '💰', label: t.moneyGoodsReceiptAnalysis || 'Migrant Income Analysis' },
    { id: 'moneyGoodsSender', icon: '👨‍👩‍👧‍👦', label: t.moneyGoodsSenderAnalysis || 'Income Source Analysis' },
    { id: 'moneyGoodsSending', icon: '📤', label: t.moneyGoodsSendingAnalysis || 'Migrant Remittance Analysis' },
    { id: 'moneyGoodsRecipient', icon: '🏦', label: t.moneyGoodsRecipientAnalysis || 'Remittance Destination Analysis' },
    { id: 'moneyPurpose', icon: '💼', label: t.moneyPurposeAnalysis || 'Received Money Usage Analysis' },
    { id: 'moneySendingPurpose', icon: '💸', label: t.moneySendingPurposeAnalysis || 'Remittance Purpose Analysis' },
    { id: 'parkLayout', icon: '🏙️', label: t.parkLayout || '园区布局' },
    { id: 'contactInfo', icon: '📞', label: t.contactInfo || '联系信息' },
    { id: 'parkHighlights', icon: '✨', label: t.parkHighlights || '园区亮点' }
  ] : [
    { id: 'map', icon: '🗺️', label: t.map || '地图' },
    { id: 'overview', icon: '📋', label: t.overview || '概览' },
    { id: 'uzTransport', icon: '🚚', label: t.transportConnections || '交通分析' },
    { id: 'contactInfo', icon: '📞', label: t.contactInfo || '联系信息' }
  ];

  const handleSectionChange = (sectionId) => {
    // 如果从其他视图切换到地图视图，标记不需要飞入动画
    if (sectionId === 'map' && activeSection !== 'map') {
      // 在切换回地图视图时，设置一个标记来避免飞入动画
      sessionStorage.setItem('parkDetailSwitchedToMap', 'true');

      // 如果地图已经加载，直接调整到园区边界
      if (mapRef.current) {
        setTimeout(() => {
          try {
            const bounds = L.polygon(pinkPolygonCoords).getBounds();
            // 使用fitBounds而不是flyToBounds，避免动画
            mapRef.current.fitBounds(bounds, { padding: [30, 30] });

            // 切换回地图视图时，重新打开标记弹窗
            if (parkMarkerRef.current) {
              // 等待地图调整完成后再打开弹窗
              setTimeout(() => {
                try {
                  parkMarkerRef.current.openPopup();
                  console.log('切换回地图视图时自动打开了园区标记弹窗');
                } catch (error) {
                  console.error('切换回地图视图时打开标记弹窗出错:', error);
                }
              }, 500);
            }
          } catch (error) {
            console.error("调整地图边界出错:", error);
          }
        }, 100);
      }
    }

    setActiveSection(sectionId);
    setIsMapView(sectionId === 'map');

    // 切换视图后，尝试校正地图尺寸
    if (sectionId === 'map') {
      invalidateMapSizeSafely();
    }
  };

  // 添加一个新函数来处理劳动力趋势图层的切换
  const toggleLaborTrendLayer = () => {
    const newState = !showLaborTrendLayer;
    setShowLaborTrendLayer(newState);

    // 当显示劳动力趋势图层时，自动缩放到合适的视图
    if (newState && laborGeoData && mapRef.current) {
      const layer = L.geoJSON(laborGeoData);
      const bounds = layer.getBounds();
      mapRef.current.flyToBounds(bounds, {
        padding: [20, 20],
        duration: 1.5,
        easeLinearity: 0.5
      });
    }
  };

  // 添加一个新函数来处理交通图层的切换
  const toggleTransportLayer = () => {
    const newState = !showTransportLayer;
    setShowTransportLayer(newState);

    // 当显示交通图层时，自动缩放到合适的视图，但缩放程度适中
    if (newState && mapRef.current) {
      mapRef.current.flyTo(park.position, 9, {
        duration: 1.5,
        easeLinearity: 0.5
      });
    }
  };

  // 处理地图样式切换
  const handleMapStyleChange = (style) => {
    setMapStyle(style);
    console.log("切换地图样式为:", style);
  };

  // 切换环境监测图层显示状态
  const toggleEnvironmentLayer = () => {
    const newState = !showEnvironmentLayer;
    setShowEnvironmentLayer(newState);

    // 当显示环境监测图层时，自动适应视图
    if (newState && environmentData && mapRef.current) {
      const layer = L.geoJSON(environmentData);
      const bounds = layer.getBounds();
      mapRef.current.flyToBounds(bounds, {
        padding: [20, 20],
        duration: 1.5,
        easeLinearity: 0.5
      });
    }
  };

  // 添加环境监测类型切换函数
  const changeEnvironmentType = (type) => {
    setEnvironmentType(type);
  };

  // 天气模块切换函数
  const toggleWeatherModule = () => {
    setShowWeatherModule(!showWeatherModule);
  };

  // 迁移类型图层切换函数
  const toggleMigrationLayer = () => {
    const newState = !showMigrationLayer;
    setShowMigrationLayer(newState);

    // 如果激活了迁移层，等待数据加载，然后调整地图视角
    if (newState) {
      console.log("Migration layer activated");

      // 重置选择状态，以确保所有迁移数据都显示
      setSelectedGender('Total');
      setSelectedMigrationType('All');

      // 延迟以确保组件已更新
      setTimeout(() => {
        if (mapRef.current) {
          // MigrationTypeLayer组件本身会处理地图视角，
          // 但如果有需要可以在这里添加额外的地图视角调整
          console.log("Migration controls activated");
        }
      }, 500);
    }
  };

  // 性别筛选器切换函数
  const handleGenderChange = (gender) => {
    setSelectedGender(gender);
  };

  // 迁移类型筛选器切换函数
  const handleMigrationTypeChange = (type) => {
    setSelectedMigrationType(type);
  };

  // 统一管理各分支的 return
  const renderSidebarContent = () => {
    switch (activeSection) {
      case 'overview':
        return <ParkOverview park={park} t={t} />;
      case 'laborTrends':
        return (
          <div className="labor-data-container">
            <div className="labor-charts-wrapper">
              <div className="labor-trend-section">
                <LaborTrendDataPanel
                  show={true}
                  laborTrends={laborTrends}
                  selectedIndex={selectedTrendIndex}
                  onChangeIndex={setSelectedTrendIndex}
                  t={t}
                />
              </div>
            </div>
          </div>
        );
      case 'laborMarket':
        return (
          <LaborDataCharts parkName={currentParkName} language={language} />
        );
      case 'laborEconomics':
        return (
          <div className="labor-economics-container">
            <h2>{t.laborEconomics || '劳动力经济分析'}</h2>
            <LaborEconomicsAnalysis parkName={currentParkName} t={t} />
          </div>
        );
      case 'populationMigration':
        return (
          <PopulationMigrationAnalysis parkName={currentParkName} language={language} t={t} />
        );
      case 'employmentMigration':
        return (
          <EmploymentMigrationAnalysis t={t} />
        );
      case 'householdRegistration':
        return (
          <HouseholdRegistrationAnalysis parkName={currentParkName} language={language} t={t} />
        );
      case 'educationLevel':
        return (
          <EducationLevelAnalysis parkName={currentParkName} language={language} t={t} />
        );
      case 'maritalStatus':
        return (
          <MaritalStatusAnalysis parkName={currentParkName} language={language} t={t} />
        );
      case 'migrationType':
        return (
          <MigrationTypeAnalysis parkName={currentParkName} language={language} t={t} />
        );
      case 'migrationFrequency':
        return (
          <MigrationFrequencyAnalysis parkName={currentParkName} language={language} t={t} />
        );
      case 'expectedStay':
        return (
          <ExpectedStayAnalysis parkName={currentParkName} language={language} t={t} />
        );
      case 'futureMigrationReason':
        return (
          <FutureMigrationReasonAnalysis parkName={currentParkName} language={language} t={t} />
        );
      case 'moneyGoodsReceipt':
        return (
          <MoneyGoodsReceiptAnalysis parkName={currentParkName} language={language} t={t} />
        );
      case 'moneyGoodsSender':
        return (
          <MoneyGoodsSenderAnalysis parkName={currentParkName} language={language} t={t} />
        );
      case 'moneyGoodsSending':
        return (
          <MoneyGoodsSendingAnalysis parkName={currentParkName} language={language} t={t} />
        );
      case 'moneyGoodsRecipient':
        return (
          <MoneyGoodsRecipientAnalysis parkName={currentParkName} language={language} t={t} />
        );
      case 'moneyPurpose':
        return (
          <MoneyPurposeAnalysis parkName={currentParkName} language={language} t={t} />
        );
      case 'moneySendingPurpose':
        return (
          <MoneySendingPurposeAnalysis parkName={currentParkName} language={language} t={t} />
        );
      case 'parkLayout':
        return (
          <div className="park-layout">
            <h2>{t.parkLayout || '园区布局'}</h2>
            {!imagesLoaded.layout && (
              <div className="image-placeholder layout-placeholder">
                <div className="loading-spinner"></div>
              </div>
            )}
            <img
              src="/images/304_layout.png"
              alt="304 Industrial Park Layout"
              className={`layout-image ${imagesLoaded.layout ? 'loaded' : 'loading'}`}
              onLoad={() => handleImageLoad('layout')}
              onError={(e) => handleImageError(e, 'layout')}
            />
          </div>
        );
      case 'uzTransport':
        return (
          <UzTransportAnalysis t={t} />
        );
      case 'contactInfo':
        return (
          <section className="contact-section">
            <h2>{t.contactInfo}</h2>
            <div className="contact-main-grid">
              <div className="contact-item">
                <h3><i className="contact-icon">🌐</i> {t.website}</h3>
                <p>
                  <a href={park.extra_details?.contact_info?.website} target="_blank" rel="noopener noreferrer">
                    {park.extra_details?.contact_info?.website || t.unknown}
                  </a>
                </p>
              </div>
              <div className="contact-item">
                <h3><i className="contact-icon">📞</i> {t.callCenter}</h3>
                <p>{park.extra_details?.contact_info?.call_center || t.unknown}</p>
              </div>
              <div className="contact-item">
                <h3><i className="contact-icon">📍</i> {t.location}</h3>
                <p>{park.province}</p>
                {park.position && (
                  <div className="coordinates">
                    <p><strong>{t.coordinates}:</strong></p>
                    <p>{t.latitude}: {park.position[0]}</p>
                    <p>{t.longitude}: {park.position[1]}</p>
                  </div>
                )}
              </div>
              <div className="contact-phones">
                <h3><i className="contact-icon">📱</i> {t.mobileNumbers}</h3>
                <div className="phone-numbers-grid">
                  {park.extra_details?.contact_info?.mobile_numbers &&
                    Object.entries(park.extra_details.contact_info.mobile_numbers).map(([lang, number], index) => (
                      <div key={index} className="phone-number-item">
                        <strong>{lang}:</strong> {number}
                      </div>
                    ))}
                </div>
              </div>
            </div>
          </section>
        );
      case 'parkHighlights':
        return (
          <section className="park-highlights-section">
            <h2>{t.parkHighlights}</h2>
            <div className="highlights-grid">
              <div className="highlight-card">
                <div className="highlight-icon">💧</div>
                <h3>{t.richWaterResources}</h3>
                <p>{t.waterResourcesDesc}</p>
              </div>
              <div className="highlight-card">
                <div className="highlight-icon">⚡</div>
                <h3>{t.stablePower}</h3>
                <p>{t.stablePowerDesc}</p>
              </div>
              <div className="highlight-card">
                <div className="highlight-icon">🏙️</div>
                <h3>{t.convenientFacilities}</h3>
                <p>{t.convenientFacilitiesDesc}</p>
              </div>
              <div className="highlight-card">
                <div className="highlight-icon">🌏</div>
                <h3>{t.strategicLocation}</h3>
                <p>{t.strategicLocationDesc}</p>
              </div>
            </div>
          </section>
        );
      case 'map':
      default:
        return (
          <div className="labor-trend-data-panel" style={{ border: 'none', boxShadow: 'none' }}>
            <h2>{t.LaborTrends || 'Labor Trends'}</h2>
            <LaborTrendDataPanel
              show={true}
              laborTrends={laborTrends}
              selectedIndex={selectedTrendIndex}
              onChangeIndex={setSelectedTrendIndex}
              t={t}
            />
          </div>
        );
    }
  };

  return (
    <div className={`park-detail-page ${mapStyle === 'night' ? 'map-night-mode' : ''}`}>
      {/* 优化后的侧边栏 - 扁平化两层结构 */}
      <div className="detail-sidebar">
        <div className="detail-sidebar-header">
          <button className="detail-back-button" onClick={() => {
            // 检查是否有保存的原始地图状态
            const originalMapState = localStorage.getItem('originalMapState');
            if (originalMapState) {
              // 确保 originalMapState 存在，主界面会自动检测并恢复
              console.log('Original map state exists, main page will restore it');

              // 确保数据有效
              try {
                const state = JSON.parse(originalMapState);
                console.log(`Returning to map with state: center=${state.mapCenter}, zoom=${state.mapZoom}`);

                // 再次保存一份到 mapState，以确保有备份
                localStorage.setItem('mapState', originalMapState);

                // 设置一个标记，表示正在返回主界面
                sessionStorage.setItem('returningToMainMap', 'true');
              } catch (e) {
                console.error('Error parsing originalMapState:', e);
                // 如果解析失败，重新创建一个默认状态
                localStorage.removeItem('originalMapState');

                // 创建一个默认状态
                const defaultState = {
                  selectedCountry: 'THAILAND', // 默认使用泰国，因为这是泰国园区详情页
                  mapCenter: [13.7563, 100.5018], // 曼谷坐标
                  mapZoom: 6
                };
                const stateJson = JSON.stringify(defaultState);
                localStorage.setItem('originalMapState', stateJson);
                localStorage.setItem('mapState', stateJson);
                console.log('Created default map state for return navigation');

                // 设置一个标记，表示正在返回主界面
                sessionStorage.setItem('returningToMainMap', 'true');
              }
            } else {
              console.log('No original map state found, creating one');
              // 如果没有原始状态，创建一个默认状态
              const defaultState = {
                selectedCountry: 'THAILAND', // 默认使用泰国，因为这是泰国园区详情页
                mapCenter: [13.7563, 100.5018], // 曼谷坐标
                mapZoom: 6
              };
              const stateJson = JSON.stringify(defaultState);
              localStorage.setItem('originalMapState', stateJson);
              localStorage.setItem('mapState', stateJson);
              console.log('Created default map state for return navigation');

              // 设置一个标记，表示正在返回主界面
              sessionStorage.setItem('returningToMainMap', 'true');
            }
            // 返回主界面
            navigate('/main');
          }}>
            <span className="back-icon">←</span> {t.backToMap}
          </button>
          {renderParkSpecificHeader(park)}
        </div>
        <nav className="detail-sidebar-nav">
          {sidebarNavItems.map(item => (
            <div
              key={item.id}
              className={`detail-sidebar-nav-item ${activeSection === item.id ? 'active' : ''}`}
              onClick={() => handleSectionChange(item.id)}
            >
              <span className="detail-sidebar-nav-icon">{item.icon}</span>
              <span className="detail-sidebar-nav-label">{item.label}</span>
            </div>
          ))}
        </nav>
        <button className="detail-language-toggle" onClick={toggleLanguage}>
          {language === 'zh' ? t.switchToEnglish : t.switchToChinese}
        </button>
      </div>

      <div className="main-content">
        {isMapView ? (
          <div className="park-map-container">
            <div className="map-wrapper">
                <MapContainer
                center={mapCenter}
                zoom={mapZoom}
                attributionControl={false}
                style={{ height: '100%', width: '100%' }}
                ref={mapRef}
                whenCreated={(map) => {
                  mapRef.current = map;
                  map.doubleClickZoom.disable();
                  console.log('地图创建完成，但不自动打开弹窗');

                  // 添加地图加载完成事件监听器
                  map.on('load', () => {
                    console.log('地图完全加载完成');
                    invalidateMapSizeSafely();
                  });

                  // 初次创建后立即尝试一次尺寸更新，避免初始右侧空白
                  invalidateMapSizeSafely();
                }}
                zoomControl={false}
              >
                <TileLayer
                  url={mapTileUrls[mapStyle]}
                  attribution={mapAttributions[mapStyle]}
                />
                <MapStyleSwitcher
                  currentStyle={mapStyle}
                  onStyleChange={handleMapStyleChange}
                  t={t}
                />
                <FacilitySearch
                  position={park.position}
                  language={language}
                  t={t}
                />
                <DetailMapZoomControl isDarkMode={mapStyle === 'night'} />
                <WeatherModule
                  position={park.position}
                  language={language}
                  t={t}
                  show={showWeatherModule}
                />
                {park.position && (
                  <Marker
                    position={park.position}
                    icon={svgIcon('/markers/marker-park.svg')}
                    ref={(el) => {
                      // 只设置标记引用，不打开弹窗
                      parkMarkerRef.current = el;
                    }}
                    eventHandlers={{
                      click: () => {
                        if (mapRef.current) {
                          if (window.markerClickTimeout) {
                            clearTimeout(window.markerClickTimeout);
                          }
                          window.markerClickTimeout = setTimeout(() => {
                            const bounds = L.polygon(pinkPolygonCoords).getBounds();
                            mapRef.current.flyToBounds(bounds, { duration: 1.5, easeLinearity: 0.5, padding: [20, 20] });
                          }, 300);
                        }
                      }
                    }}
                  />
                )}

                {showTransportLayer && (
                  <>
                    {park.port_coordinates && (
                      <Marker
                        position={park.port_coordinates}
                        icon={svgIcon('/markers/marker-port.svg')}
                      >
                        <Popup>
                          <strong>{park.port}</strong><br />
                          {t.distance}: {park.extra_details?.distance?.["Laem Chabang Port"]}
                        </Popup>
                      </Marker>
                    )}

                    {park.airport_coordinates && (
                      <Marker
                        position={park.airport_coordinates}
                        icon={svgIcon('/markers/marker-airport.svg')}
                      >
                        <Popup>
                          <strong>{park.airport}</strong><br />
                          {t.distance}: {park.extra_details?.distance?.["Suvarnabhumi Airport"]}
                        </Popup>
                      </Marker>
                    )}

                    {park.city_coordinates && (
                      <Marker
                        position={park.city_coordinates}
                        icon={svgIcon('/markers/marker-city.svg')}
                      >
                        <Popup>
                          <strong>{park.city}</strong><br />
                          {t.distance}: {park.extra_details?.distance?.["Bangkok (Asok)"]}
                        </Popup>
                      </Marker>
                    )}

                    {park.position && park.port_coordinates && (
                      <>
                        <Polyline
                          positions={[park.position, park.port_coordinates]}
                          color="#2196F3"
                          weight={3}
                          opacity={0.7}
                          dashArray="5, 10"
                        />
                        {/* 在虚线中心显示公里数 */}
                        {(() => {
                          const midPoint = L.latLng(
                            (park.position[0] + park.port_coordinates[0]) / 2,
                            (park.position[1] + park.port_coordinates[1]) / 2
                          );
                          const distanceValue = park.extra_details?.distance?.["Laem Chabang Port"] ||
                                         calculateDistance(park.position, park.port_coordinates);
                          const distance = typeof distanceValue === 'string' ?
                                         distanceValue : formatDistance(distanceValue, 'metric');
                          return (
                            <Marker
                              position={midPoint}
                              icon={L.divIcon({
                                className: 'transport-distance-marker',
                                html: `<div class="distance-bubble">${distance}</div>`,
                                iconSize: [80, 20],
                                iconAnchor: [40, 10]
                              })}
                            />
                          );
                        })()}
                      </>
                    )}
                    {park.position && park.airport_coordinates && (
                      <>
                        <Polyline
                          positions={[park.position, park.airport_coordinates]}
                          color="#9C27B0"
                          weight={3}
                          opacity={0.7}
                          dashArray="5, 10"
                        />
                        {/* 在虚线中心显示公里数 */}
                        {(() => {
                          const midPoint = L.latLng(
                            (park.position[0] + park.airport_coordinates[0]) / 2,
                            (park.position[1] + park.airport_coordinates[1]) / 2
                          );
                          const distanceValue = park.extra_details?.distance?.["Suvarnabhumi Airport"] ||
                                         calculateDistance(park.position, park.airport_coordinates);
                          const distance = typeof distanceValue === 'string' ?
                                          distanceValue : formatDistance(distanceValue, 'metric');
                          return (
                            <Marker
                              position={midPoint}
                              icon={L.divIcon({
                                className: 'transport-distance-marker',
                                html: `<div class="distance-bubble">${distance}</div>`,
                                iconSize: [80, 20],
                                iconAnchor: [40, 10]
                              })}
                            />
                          );
                        })()}
                      </>
                    )}
                    {park.position && park.city_coordinates && (
                      <>
                        <Polyline
                          positions={[park.position, park.city_coordinates]}
                          color="#FF9800"
                          weight={3}
                          opacity={0.7}
                          dashArray="5, 10"
                        />
                        {/* 在虚线中心显示公里数 */}
                        {(() => {
                          const midPoint = L.latLng(
                            (park.position[0] + park.city_coordinates[0]) / 2,
                            (park.position[1] + park.city_coordinates[1]) / 2
                          );
                          const distanceValue = park.extra_details?.distance?.["Bangkok (Asok)"] ||
                                         calculateDistance(park.position, park.city_coordinates);
                          const distance = typeof distanceValue === 'string' ?
                                           distanceValue : formatDistance(distanceValue, 'metric');
                          return (
                            <Marker
                              position={midPoint}
                              icon={L.divIcon({
                                className: 'transport-distance-marker',
                                html: `<div class="distance-bubble">${distance}</div>`,
                                iconSize: [80, 20],
                                iconAnchor: [40, 10]
                              })}
                            />
                          );
                        })()}
                      </>
                    )}

                    <div className="transport-legend">
                      <div className="legend-item">
                        <img src="/markers/marker-port.svg" alt="港口" />
                        <span>{t.port || "港口"}</span>
                      </div>
                      <div className="legend-item">
                        <img src="/markers/marker-airport.svg" alt="机场" />
                        <span>{t.airport || "机场"}</span>
                      </div>
                      <div className="legend-item">
                        <img src="/markers/marker-city.svg" alt="城市" />
                        <span>{t.city || "城市"}</span>
                      </div>
                      <div className="legend-item">
                        <img src="/markers/marker-park.svg" alt="园区" />
                        <span>{t.industrialPark || "产业园区"}</span>
                      </div>
                    </div>
                  </>
                )}
                {isThai && showBoundary && (
                  <Polygon
                    positions={pinkPolygonCoords}
                    pathOptions={{
                      color: 'magenta',
                      weight: 2,
                      fillColor: 'pink',
                      fillOpacity: 0.2,
                    }}
                  />
                )}
                {isThai && (
                  <LaborTrendOverlay
                    geoData={laborGeoData}
                    fillColor={fillColor}
                    show={showLaborTrendLayer}
                  />
                )}
                <MapController center={park.position} zoom={12} />
                {showRulerTool && <RulerTool active={showRulerTool} onClose={() => setShowRulerTool(false)} />}
                <ScaleControl
                  position="bottomleft"
                  imperial={false}
                  metric={true}
                  maxWidth={100}
                />
                <EnvironmentMonitoringLayer
                  geoData={environmentData}
                  show={showEnvironmentLayer}
                  type={environmentType}
                  t={t}
                />
                <MigrationTypeLayer
                  show={showMigrationLayer}
                  selectedGender={selectedGender}
                  selectedMigrationType={selectedMigrationType}
                  onGenderChange={handleGenderChange}
                  onMigrationTypeChange={handleMigrationTypeChange}
                  t={t}
                />
                {/* 时间序列按钮 - 移动到测量工具按钮上方 */}
              </MapContainer>
              {isThai && (
                <MapTimeSlider
                  laborTrends={laborTrends}
                  selectedIndex={selectedTrendIndex}
                  onChangeIndex={setSelectedTrendIndex}
                  show={showLaborTrendLayer}
                />
              )}
              <button
                className={`ruler-button ${showRulerTool ? 'active' : ''}`}
                onClick={() => setShowRulerTool(!showRulerTool)}
                title={showRulerTool ? t.hideRuler || "隐藏测量工具" : t.showRuler || "显示测量工具"}
              >
                📏
              </button>

              {isThai && (
                <button
                  className={`time-series-button ${showTimeSeriesPanel ? 'active' : ''}`}
                  onClick={() => setShowTimeSeriesPanel(!showTimeSeriesPanel)}
                  title={showTimeSeriesPanel ? t.hideTimeSeries || "隐藏时间序列" : t.showTimeSeries || "显示时间序列"}
                >
                  📈
                </button>
              )}

              {/* 天气按钮已移至WeatherModule组件中 */}

              {isThai && (
                <div className="map-side-controls">
                  <button
                    className={`toggle-layer-button ${showLaborTrendLayer ? 'active' : ''}`}
                    onClick={toggleLaborTrendLayer}
                  >
                    {showLaborTrendLayer ? t.hideLaborTrend || "隐藏劳动力趋势" : t.showLaborTrend || "显示劳动力趋势"}
                  </button>
                  <div style={{ position: 'relative' }}>
                    <div className={`environment-type-selector ${showEnvironmentLayer ? 'active' : ''}`}>
                      <button
                        className={`env-type-btn ${environmentType === 'air' ? 'active' : ''}`}
                        onClick={() => changeEnvironmentType('air')}
                        title={t.airQuality || "空气质量"}
                      >
                        {t.airQuality || "空气质量"}
                      </button>
                      <button
                        className={`env-type-btn ${environmentType === 'water' ? 'active' : ''}`}
                        onClick={() => changeEnvironmentType('water')}
                        title={t.waterQuality || "水质"}
                      >
                        {t.waterQuality || "水质"}
                      </button>
                      <button
                        className={`env-type-btn ${environmentType === 'noise' ? 'active' : ''}`}
                        onClick={() => changeEnvironmentType('noise')}
                        title={t.noiseLevel || "噪声"}
                      >
                        {t.noiseLevel || "噪声"}
                      </button>
                      <button
                        className={`env-type-btn ${environmentType === 'wind' ? 'active' : ''}`}
                        onClick={() => changeEnvironmentType('wind')}
                        title={t.windDirection || "风向风速"}
                      >
                        {t.windDirection || "风向风速"}
                      </button>
                    </div>
                    <button
                      className={`toggle-environment-button ${showEnvironmentLayer ? 'active' : ''}`}
                      onClick={toggleEnvironmentLayer}
                    >
                      {showEnvironmentLayer ? t.hideEnvironment || '隐藏环境' : t.showEnvironment || '显示环境'}
                    </button>
                  </div>
                  <button
                    className={`toggle-transport-button ${showTransportLayer ? 'active' : ''}`}
                    onClick={toggleTransportLayer}
                  >
                    {showTransportLayer ? t.hideTransport || "隐藏交通图层" : t.showTransport || "显示交通图层"}
                  </button>

                  {/* 迁移类型图层控制 */}
                  <button
                    className={`toggle-layer-button ${showMigrationLayer ? 'active' : ''}`}
                    onClick={toggleMigrationLayer}
                  >
                    {showMigrationLayer ? (t.hideMigration || 'Hide Migration Data') : (t.showMigration || 'Show Migration Data')}
                  </button>

                  <button className="reset-map-button" onClick={resetMap}>
                    {t.resetMapView}
                  </button>
                </div>
              )}
            </div>
          </div>
        ) : (
          <div className="content-container">{renderSidebarContent()}</div>
        )}
      </div>

      {/* 添加时间序列面板 */}
      <TimeSeriesPanel
        show={showTimeSeriesPanel}
        onClose={() => setShowTimeSeriesPanel(false)}
        laborTrends={laborTrends}
        selectedIndex={selectedTrendIndex}
        onChangeIndex={setSelectedTrendIndex}
        t={t}
      />
    </div>
  );
};

export default ParkDetailPage;
