import React, { useEffect, useMemo, useState } from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';
import { Line } from 'react-chartjs-2';
import '../../../styles/TimeSeriesPanel.css';

ChartJS.register(CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend);

// Helper: extract series from a record by keys matcher and sorted labels
function extractSeries(record, keyMatcher, labelSortFn) {
  const entries = Object.entries(record)
    .filter(([k, v]) => keyMatcher(k) && (typeof v === 'number'))
    .map(([k, v]) => ({ label: k, value: v }));
  entries.sort((a, b) => labelSortFn(a.label, b.label));
  return entries;
}

function sortQuarterLabels(a, b) {
  // a: '2024-Q1'
  const [ay, aq] = a.split('-Q');
  const [by, bq] = b.split('-Q');
  if (ay !== by) return Number(ay) - Number(by);
  return Number(aq) - Number(bq);
}

function sortYearLabels(a, b) {
  return Number(a) - Number(b);
}

const palette = [
  '#3b82f6', '#ef4444', '#10b981', '#f59e0b', '#8b5cf6', '#06b6d4', '#e11d48'
];

const REGION_PRESETS = [
  'Republic of Uzbekistan',
  'Tashkent region',
  'Tashkent city',
];

const UzTransportAnalysis = ({ defaultRegions = ['Tashkent region', 'Tashkent city'], t = {} }) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [quarterlyTurnover, setQuarterlyTurnover] = useState([]); // Freight turnover of road transport (quarterly)
  const [annualTurnoverRoad, setAnnualTurnoverRoad] = useState([]); // Freight turnover volume of road transport (annual)
  const [annualCargoVolumeRoad, setAnnualCargoVolumeRoad] = useState([]); // Volume of cargo transportation by road (annual)
  const [totalQuarterly, setTotalQuarterly] = useState([]); // Freight turnover, total (quarterly)
  const [meta, setMeta] = useState({});
  const [selectedRegions, setSelectedRegions] = useState(defaultRegions);

  useEffect(() => {
    let cancelled = false;
    setLoading(true);
    (async () => {
      try {
        const [qRes, aTurnRes, aVolRes, totalQRes] = await Promise.all([
          fetch('/data/Uzbekistan_data/Transportation/Freight turnover of road transport (quarterly).json'),
          fetch('/data/Uzbekistan_data/Transportation/Freight turnover volume of road transport (annual).json'),
          fetch('/data/Uzbekistan_data/Transportation/Volume of cargo transportation by road (annual).json'),
          fetch('/data/Uzbekistan_data/Transportation/Freight turnover, total (quarterly).json'),
        ]);
        const [qJson, aTurnJson, aVolJson, totalQJson] = await Promise.all([
          qRes.json(), aTurnRes.json(), aVolRes.json(), totalQRes.json()
        ]);
        if (cancelled) return;
        setQuarterlyTurnover(Array.isArray(qJson["Ma'lumot"]) ? qJson["Ma'lumot"] : []);
        setAnnualTurnoverRoad(Array.isArray(aTurnJson["Ma'lumot"]) ? aTurnJson["Ma'lumot"] : []);
        setAnnualCargoVolumeRoad(Array.isArray(aVolJson["Ma'lumot"]) ? aVolJson["Ma'lumot"] : []);
        setTotalQuarterly(Array.isArray(totalQJson["Ma'lumot"]) ? totalQJson["Ma'lumot"] : []);
        setMeta({});
        setLoading(false);
      } catch (e) {
        console.error('Error loading transport data', e);
        if (!cancelled) {
          setError('Failed to load Uzbekistan transport data');
          setLoading(false);
        }
      }
    })();
    return () => { cancelled = true; };
  }, []);

  const availableRegions = useMemo(() => {
    const set = new Set();
    [...quarterlyTurnover, ...annualTurnoverRoad, ...annualCargoVolumeRoad].forEach(r => {
      if (r?.Klassifikator_en) set.add(r.Klassifikator_en);
    });
    // Keep common presets on top
    const arr = Array.from(set);
    arr.sort((a, b) => {
      const ai = REGION_PRESETS.indexOf(a);
      const bi = REGION_PRESETS.indexOf(b);
      if (ai !== -1 || bi !== -1) return (ai === -1 ? 999 : ai) - (bi === -1 ? 999 : bi);
      return a.localeCompare(b);
    });
    return arr;
  }, [quarterlyTurnover, annualTurnoverRoad, annualCargoVolumeRoad]);

  const toggleRegion = (region) => {
    setSelectedRegions(prev => prev.includes(region) ? prev.filter(r => r !== region) : [...prev, region]);
  };

  const regionColor = (region) => {
    const idx = REGION_PRESETS.includes(region)
      ? REGION_PRESETS.indexOf(region)
      : (availableRegions.indexOf(region) % palette.length);
    return palette[idx % palette.length];
  };

  const buildDataset = (records, labelExtractor, keyMatcher, sorter) => {
    const labelsSet = new Set();
    const seriesByRegion = {};

    selectedRegions.forEach(region => {
      const rec = records.find(r => r.Klassifikator_en === region);
      if (!rec) return;
      const points = extractSeries(rec, keyMatcher, sorter);
      points.forEach(p => labelsSet.add(p.label));
      seriesByRegion[region] = points.reduce((acc, p) => ({ ...acc, [p.label]: p.value }), {});
    });

    const labels = Array.from(labelsSet);
    labels.sort(sorter);

    const datasets = selectedRegions
      .map(region => {
        const color = regionColor(region);
        return {
          label: region,
          data: labels.map(l => seriesByRegion[region]?.[l] ?? null),
          borderColor: color,
          backgroundColor: color + '55',
          tension: 0.25,
          spanGaps: true,
        };
      });

    return { labels, datasets };
  };

  const lineOptions = (title, yLabel) => ({
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: { position: 'bottom' },
      title: { display: true, text: title },
      tooltip: {
        callbacks: {
          label: (ctx) => {
            const v = ctx.parsed.y;
            const num = (v ?? 0).toLocaleString();
            return `${ctx.dataset.label}: ${num}${yLabel ? ' ' + yLabel : ''}`;
          }
        }
      }
    },
    scales: {
      x: { ticks: { maxRotation: 0, autoSkip: true } },
      y: { beginAtZero: true }
    }
  });

  if (loading) {
    return <div className="time-series-panel" style={{ padding: 16 }}>{t.loading || '加载中...'}</div>;
  }
  if (error) {
    return <div className="time-series-panel" style={{ padding: 16, color: 'red' }}>{t.dataLoadError || '数据加载失败'}</div>;
  }

  const quarterlyData = buildDataset(
    quarterlyTurnover,
    (r) => r.Klassifikator_en,
    (k) => /^\d{4}-Q[1-4]$/.test(k),
    sortQuarterLabels
  );

  const annualTurnoverData = buildDataset(
    annualTurnoverRoad,
    (r) => r.Klassifikator_en,
    (k) => /^\d{4}$/.test(k),
    sortYearLabels
  );

  const annualCargoData = buildDataset(
    annualCargoVolumeRoad,
    (r) => r.Klassifikator_en,
    (k) => /^\d{4}$/.test(k),
    sortYearLabels
  );

  const unitQuarterly = "million. ton-km"; // from metadata
  const unitAnnualTurnover = "million t-km";
  const unitAnnualCargo = "mln. Tons";

  // Total quarterly line (single series)
  const totalQuarterlySeries = (() => {
    const rec = totalQuarterly[0];
    if (!rec) return { labels: [], datasets: [] };
    const points = extractSeries(rec, (k) => /^\d{4}-Q[1-4]$/.test(k), sortQuarterLabels);
    const labels = points.map(p => p.label);
    const datasets = [{
      label: 'Freight turnover, total (quarterly) — Uzbekistan',
      data: points.map(p => p.value),
      borderColor: '#111827',
      backgroundColor: '#11182755',
      tension: 0.25,
    }];
    return { labels, datasets };
  })();

  return (
    <section className="time-series-panel">
      <h2 style={{ marginBottom: 8 }}>{t.transportConnections || 'Transport Analysis'}</h2>

      {/* Region selector */}
      <div className="filter-section" style={{ marginBottom: 12 }}>
        <div style={{ fontWeight: 600, marginBottom: 8 }}>{t.region || 'Region'}</div>
        <div style={{ display: 'flex', flexWrap: 'wrap', gap: 8 }}>
          {availableRegions.slice(0, 12).map(region => (
            <button
              key={region}
              className={`category-button ${selectedRegions.includes(region) ? 'active' : ''}`}
              style={{ borderColor: regionColor(region) }}
              onClick={() => toggleRegion(region)}
            >
              <span style={{ display: 'inline-block', width: 10, height: 10, background: regionColor(region), borderRadius: 2, marginRight: 6 }} />
              {region}
            </button>
          ))}
        </div>
      </div>

      {/* Total quarterly */}
      <div className="chart-container" style={{ height: 220 }}>
        <Line data={totalQuarterlySeries} options={lineOptions('Freight turnover, total (quarterly)', 'bln. ton-km')} />
      </div>

      {/* Charts by region */}
      <div className="chart-container" style={{ height: 260 }}>
        <Line data={quarterlyData} options={lineOptions('Freight turnover of road transport (quarterly)', unitQuarterly)} />
      </div>

      <div className="chart-container" style={{ height: 260 }}>
        <Line data={annualTurnoverData} options={lineOptions('Freight turnover volume of road transport (annual)', unitAnnualTurnover)} />
      </div>

      <div className="chart-container" style={{ height: 260 }}>
        <Line data={annualCargoData} options={lineOptions('Volume of cargo transportation by road (annual)', unitAnnualCargo)} />
      </div>

      <div className="data-source-info" style={{ marginTop: 12, fontSize: 12, color: '#667' }}>
        <div>{t.dataSource || '数据来源'}: Statistics Agency under the President of the Republic of Uzbekistan</div>
      </div>
    </section>
  );
};

export default UzTransportAnalysis;

